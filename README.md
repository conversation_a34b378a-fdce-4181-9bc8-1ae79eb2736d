# ATMA Backend - Microservices Ecosystem

Ekosistem backend microservices untuk platform assessment psikologi ATMA yang menggunakan Express.js.

## Arsitektur Microservices

### 1. API Gateway (`api-gateway/`)
- **Port**: 3000
- **Fungsi**: Single entry point untuk semua request dari frontend
- **Fitur**: Routing, authentication, rate limiting, logging

### 2. Auth Service (`auth-service/`)
- **Port**: 3001
- **Fungsi**: Manajemen identitas dan otentikasi pengguna
- **Fitur**: Registrasi, login, JWT token management
- **Database**: PostgreSQL (users table)

### 3. Archive Service (`archive-service/`)
- **Port**: 3002
- **Fungsi**: CRUD operations untuk hasil analisis
- **Fitur**: Menyimpan dan mengambil profil persona
- **Database**: PostgreSQL (analysis_results table)

### 4. Assessment Service (`assessment-service/`)
- **Port**: 3003
- **Fungsi**: Menerima dan memvalidasi data assessment
- **Fitur**: Validasi data, publish job ke RabbitMQ
- **Queue**: RabbitMQ

### 5. Analysis Worker (`analysis-worker/`)
- **Fungsi**: Stateless worker untuk analisis data
- **Fitur**: Consume jobs, integrasi Google GenAI, simpan hasil
- **Queue**: RabbitMQ

### 6. Notification Service (`notification-service/`)
- **Port**: 3004
- **Fungsi**: Real-time notifications via WebSocket
- **Fitur**: WebSocket connections, push notifications

## Quick Start

```bash
# Clone repository
git clone <repository-url>
cd atma-backend

# Start all services with Docker Compose
npm run dev

# Stop all services
npm run stop

# View logs
npm run logs

# Clean up (remove containers and volumes)
npm run clean
```

## Environment Variables

Setiap service memiliki file `.env` sendiri. Copy dari `.env.example` dan sesuaikan:

```bash
# Copy environment files
cp api-gateway/.env.example api-gateway/.env
cp auth-service/.env.example auth-service/.env
cp archive-service/.env.example archive-service/.env
cp assessment-service/.env.example assessment-service/.env
cp analysis-worker/.env.example analysis-worker/.env
cp notification-service/.env.example notification-service/.env
```

## Database Schema

### Users Table (Auth Service)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Analysis Results Table (Archive Service)
```sql
CREATE TABLE analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    assessment_data JSONB,
    persona_profile JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## API Endpoints

### API Gateway (Port 3000)
- `POST /auth/*` → Auth Service
- `GET|POST|PUT|DELETE /archive/*` → Archive Service  
- `POST /assessments/*` → Assessment Service
- `GET /notifications/*` → Notification Service

### Auth Service (Port 3001)
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh JWT token
- `GET /auth/profile` - Get user profile

### Archive Service (Port 3002)
- `GET /archive/results` - Get user's analysis results
- `GET /archive/results/:id` - Get specific analysis result
- `POST /archive/results` - Create new analysis result
- `PUT /archive/results/:id` - Update analysis result
- `DELETE /archive/results/:id` - Delete analysis result

### Assessment Service (Port 3003)
- `POST /assessments/submit` - Submit assessment data for analysis

### Notification Service (Port 3004)
- `GET /notifications/ws` - WebSocket connection
- `GET /notifications/status` - Service health check

## Development

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- RabbitMQ 3.12+

### Local Development
```bash
# Install dependencies for each service
cd api-gateway && npm install
cd ../auth-service && npm install
cd ../archive-service && npm install
cd ../assessment-service && npm install
cd ../analysis-worker && npm install
cd ../notification-service && npm install

# Start individual services
cd api-gateway && npm run dev
cd auth-service && npm run dev
# ... etc
```

## Testing

```bash
# Run tests for all services
npm run test

# Run tests for specific service
cd auth-service && npm test
```

## Monitoring

- **Health Checks**: Setiap service memiliki endpoint `/health`
- **Logs**: Centralized logging via Docker Compose
- **Metrics**: Prometheus metrics (optional)

## Security

- JWT authentication
- Rate limiting
- CORS configuration
- Input validation
- SQL injection protection
- XSS protection

## License

MIT License
